<?php

namespace App\Helpers;

class ApiResponse
{
    public static function success(string $message, $data = [], int $statusCode = 200)
    {
        return response()->json([
            'statusCode' => $statusCode,
            'message' => $message,
            'data' => $data
        ], $statusCode);
    }

    public static function error(string $message, int $statusCode = 500, $errors = [])
    {
        return response()->json([
            'statusCode' => $statusCode,
            'message' => $message,
            'errors' => $errors
        ], $statusCode);
    }
}