<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SyncController;

Route::middleware(['basic.auth'])->group(function () {
    Route::get('/meal-stub/{branchId}/{invoiceNo}/{outletId}/{posNo}', [SyncController::class, 'getMealStubFromPOS']);
    Route::get('/product-by-serial/{serialNumber}', [SyncController::class, 'getProductBySerial']);
    Route::post('/send-to-server', [SyncController::class, 'sendToServer']);
});



