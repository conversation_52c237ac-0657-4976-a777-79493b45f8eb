<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Resources\InParkCurrencyDetailsResource;
use App\Http\Resources\MealStubComponentResource;
use App\Http\Requests\SendToServerRequest;
use App\Helpers\ApiResponse;
use App\Models\InParkCurrencyDetails;
use App\Models\MealStubComponents;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SyncController extends Controller
{
    /**
     * GET /api/meal-stub/{invoiceNo}/{outletId}/{posNo}
     * Kukunin ung MealStubComponents sa POS na may same Invoice No, Outlet ID at POS Number.
     * 

     */
    public function getMealStubFromPOS($branchId, $invoiceNo, $outletId, $posNo, Request $request)
    {
        $inParkRecord = InParkCurrencyDetails::where([
            'BRANCHID' => $branchId,
            'INVNO' => $invoiceNo,
            'OUTLETID' => $outletId,
            'POSNO' => $posNo
        ])->firstOrFail();

        // Debug: Log the REFERENCE_ID that we're looking for
        \Log::info('Looking for MealStubComponents with REFERENCEID: ' . $inParkRecord->REFERENCE_ID);

        $mealStubComponents = MealStubComponents::where('REFERENCEID', $inParkRecord->REFERENCE_ID)->get();

        // Debug: Log how many components were found
        \Log::info('Found ' . $mealStubComponents->count() . ' meal stub components');

        if ($mealStubComponents->isEmpty()) {
            // Return more debugging info in the error response
            return ApiResponse::error('No meal stub components found', 404, [
                'debug_info' => [
                    'searched_reference_id' => $inParkRecord->REFERENCE_ID,
                    'inpark_record_found' => true,
                    'search_parameters' => [
                        'BRANCHID' => $branchId,
                        'INVNO' => $invoiceNo,
                        'OUTLETID' => $outletId,
                        'POSNO' => $posNo
                    ]
                ]
            ]);
        }

        return ApiResponse::success('Success', [
            'INPARKCURRENCYDETAILS' => new InParkCurrencyDetailsResource($inParkRecord),
            'MEALSTUBCOMPONENTS' => MealStubComponentResource::collection($mealStubComponents)
        ]);
    }

    /**
     * POST /api/send-to-server
     * UPSERT sa mga data sa target database.
     */
    public function sendToServer(SendToServerRequest $request)
    {
        $payloads = $request->validated();
        $results = [];

        DB::connection('sqlsrv')->transaction(function () use ($payloads, &$results) {
            foreach ($payloads as $item) {
                $table = $item['table'];
                $data = collect($item['data'])->pluck('value', 'column')->toArray();
                $conditions = collect($item['conditions'])->pluck('value', 'column')->toArray();
                $autoUpdate = (bool) $item['auto_update']; // ensure boolean

                try {
                    if ($autoUpdate) {
                        DB::connection('sqlsrv')
                            ->table($table)
                            ->updateOrInsert($conditions, $data);
                    } else {
                        DB::connection('sqlsrv')
                            ->table($table)
                            ->insert($data);
                    }

                    $results[] = [
                        'StatusCode' => 200,
                        'Message' => 'Success',
                        'Data' => $conditions
                    ];
                } catch (\Throwable $e) {
                    $results[] = [
                        'StatusCode' => 500,
                        'Message' => $e->getMessage(),
                        'Data' => $conditions
                    ];
                }
            }
        });

        return ApiResponse::success('Data processed successfully', $results);
    }

    /**
     * GET /api/product-by-serial/{serialNumber}
     * Kukunin ang Product ID base sa Serial Number
     *
     * @param string $serialNumber
     */
    public function getProductBySerial($serialNumber, Request $request)
    {
        // Find InParkCurrencyDetails record by serial number
        $inParkRecord = InParkCurrencyDetails::where('SERIALNUMBER', $serialNumber)->first();

        if (!$inParkRecord) {
            return ApiResponse::error('Serial number not found', 404, [
                'serial_number' => $serialNumber
            ]);
        }

        // Find meal stub components using the relationship
        $mealStubComponents = MealStubComponents::where('REFERENCEID', $inParkRecord->REFERENCE_ID)->get();

        if ($mealStubComponents->isEmpty()) {
            return ApiResponse::error('No products found for this serial number', 404, [
                'serial_number' => $serialNumber,
                'reference_id' => $inParkRecord->REFERENCE_ID
            ]);
        }

        // Extract product IDs
        $productIds = $mealStubComponents->pluck('PRODUCTID')->unique()->values();

        return ApiResponse::success('Products found', [
            'serial_number' => $serialNumber,
            'product_ids' => $productIds,
            'meal_stub_details' => MealStubComponentResource::collection($mealStubComponents)
        ]);
    }

}
