<?php

namespace App\Http\Controllers;

use App\Models\ValidatorSyncTable;
use DB;
use Exception;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;

class NewTransactionController extends Controller
{
    public function syncTable($date, $time, $posId, $source, Request $request)
    {
        $username = env('USER_NAME');
        $password = env('PASSWORD');
        $authHeader = 'Basic ' . base64_encode($username . ':' . $password);

        if ($request->header('Authorization', 'default') == $authHeader) {
            if ($date == 0 && $time == 0 && $source == 0 && $posId == 0) {
                return response()->json([
                    'StatusCode' => 400,
                    'Message' => 'Invalid request: all parameters cannot be zero',
                ], 400);
            }

            // $data = ValidatorSyncTable::where(function ($query) use ($date, $time, $source, $posId) {
            //     if ($date > 0) {
            //         $query->where('DATE', '>', $date);
            //     }

            //     $query->orWhere(function ($subQuery) use ($date, $time, $source, $posId) {
            //         $subQuery->where('DATE', '=', $date)
            //             ->where('TIME', '>', $time)
            //             ->where('SOURCE', '=', $source)
            //             ->where('POSID', '=', $posId);
            //     });
            // })
            //     ->get();
            try {
            $data = ValidatorSyncTable::whereNotNull('DATE')
                ->whereNotNull('TIME')
                ->whereNotNull('BRANCHID')
                ->whereNotNull('POSID')
                ->where(function ($query) use ($date, $time, $source, $posId) {
                    if ($date == 0 && $time == 0) {
                        // Ignore date & time; filter only by  and SOURCE
                        $query->where('BRANCHID', '=', $source)
                            ->where('POSID', '=', $posId);
                    } else {
                        $query->where('DATE', '>', $date)
                            ->orWhere(function ($subQuery) use ($date, $time, $source, $posId) {
                                $subQuery->where('DATE', '=', $date)
                                    ->where('TIME', '>', $time)
                                    ->where('BRANCHID', '=', $source)
                                    ->where('POSID', '=', $posId);
                            });
                    }
                })
                ->firstOrFail();
                } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
                    return response()->json([
                        'error' => 'ValidatorSyncTable not found',
                    ], 404);
                }

            if ($data->isEmpty()) {
                return response()->json([], 204); // Return 204 No Content if no data
            }

            $res = [];
            $foundData = false;

            foreach ($data as $item) {
                try {
                    switch (strtolower($item['TABLENAME'])) {
                        case 'tickets':
                            $response = DB::connection('retailapi')
                                ->table('TICKETS')
                                ->where('TYPE', $item['COLUMN1'])
                                ->where('TICKETNO', $item['COLUMN2'])
                                ->where('POSNO', $item['COLUMN3'])
                                ->get();

                            if ($response->isEmpty()) {
                                continue 2;
                            }

                            $foundData = true;
                            $retrieved = [];
                            foreach ($response[0] as $key => $value) {
                                $retrieved[] = [
                                    "column" => $key,
                                    "value" => $value
                                ];
                            }

                            $res[] = [
                                'statusCode' => 200,
                                'action' => $item['ACTION'],
                                'table' => 'TICKETS',
                                'message' => 'success',
                                'data' => $retrieved
                            ];
                            break;

                        case 'rentables':
                            $response = DB::connection('retailapi')
                                ->table('Rentables')
                                ->where('BRANCHID', $item['COLUMN1'])
                                ->where('OUTLETID', $item['COLUMN2'])
                                ->where('TERMID', $item['COLUMN3'])
                                ->where('TICKETBARCODE', $item['COLUMN4'])
                                ->where('PRODUCTID', $item['COLUMN5'])
                                ->where('POSCHECKIN', $item['COLUMN6'])
                                ->get();

                            if ($response->isEmpty()) {
                                continue 2;
                            }

                            $foundData = true;
                            $retrieved = [];
                            foreach ($response[0] as $key => $value) {
                                if (!in_array($key, ['ID', 'RETURNED', 'REMARKS'])) {
                                    $retrieved[] = [
                                        "column" => $key,
                                        "value" => $value
                                    ];
                                }
                            }

                            $res[] = [
                                'statusCode' => 200,
                                'action' => $item['ACTION'],
                                'table' => 'RENTABLES',
                                'message' => 'success',
                                'data' => $retrieved
                            ];
                            break;

                        default:
                            continue 2;
                    }
                } catch (QueryException $exception) {
                    $res[] = [
                        'statusCode' => 500,
                        'message' => 'Database error',
                        'ErrorCode' => $exception->getCode(),
                        'data' => [$exception->getMessage()]
                    ];
                } catch (Exception $exception) {
                    $res[] = [
                        'statusCode' => 500,
                        'message' => 'Server error',
                        'ErrorCode' => $exception->getCode(),
                        'data' => [$exception->getMessage()]
                    ];
                }
            }

            if (!$foundData) {
                return response()->json([], 204);
            }

            return response()->json($res);
        } else {
            return response()->json([
                'StatusCode' => 401,
                'Message' => 'Unauthorized',
            ], 401);
        }
    }
}
