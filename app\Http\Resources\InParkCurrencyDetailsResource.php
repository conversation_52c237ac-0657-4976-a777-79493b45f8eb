<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InParkCurrencyDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'BRANCHID' => $this->BRANCHID,
            'INVNO' => $this->INVNO,
            'OUTLETID' => $this->OUTLETID,
            'POSNO' => $this->POSNO,
            'REFERENCEID' => $this->REFERENCEID,

        ];
    }
}
