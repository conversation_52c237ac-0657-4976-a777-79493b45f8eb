<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class BasicAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
         $username = env('USER_NAME');
        $password = env('PASSWORD');
        $authHeader = 'Basic '.base64_encode($username.':'.$password);

        if ($request->header('Authorization') !== $authHeader) {
            return response()->json([
                'STATUS' => '401',
                'MESSAGE' => 'Unauthorized access. Please provide valid credentials.'
            ], 401);
        }
        return $next($request);
    }

}
