<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class InParkCurrencyDetails extends Model
{
    protected $table = 'inparkcurrencydetails';
    protected $connection = 'sqlsrv';


    protected $fillable = [
    'INPARKCURRENCYID',
    'BRANCHID',
    'OUTLETID',
    'ID',
    'TYPE',
    'STATUS',
    'INVNO',
    'SERIALNUMBER',
    'MOP<PERSON>',
    'rowguid',
    'DATECREATED',
];

    // Sync Query
    public function mealStubComponents()     {
        return $this->hasMany(MealStubComponents::class,'REFERENCEID','REFERENCE_ID');
    }
}

