<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SendToServerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // BASIC MIDDLEWARE AUTH
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            '*.table' => 'required|string',
            '*.data' => 'required|array|min:1',
            '*.data.*.column' => 'required|string',
            '*.data.*.value' => 'nullable',
            '*.conditions' => 'required|array|min:1',
            '*.conditions.*.column' => 'required|string',
            '*.conditions.*.value' => 'nullable',
            '*.auto_update' => 'required|boolean',
        ];
    }
}
