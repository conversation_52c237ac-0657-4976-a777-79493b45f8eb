<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MealStubComponents extends Model
{
    protected $table = 'mealstubcomponents';
    protected $connection = 'sqlsrv';
    public $timestamps = false;

    protected $fillable = [
        'REFERENCEID',
        'LINENO',
        'PRODUCTID',
        'PRODUCTDE<PERSON>',
        '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        'DEFAULT<PERSON>OD<PERSON>',
        'QTY',
        'ISMODIFIABLE',
        'TRLINENO',
        'UPLOADED',
        'rowguid',
        'BRANCHID',
        'OUTLETID',
        'STATUS',
    ];


  
public static function getByProductId($productId = null)
{
    $query = self::query();

    if ($productId) {
        $query->where('PRODUCTID', $productId);
    }

    return $query->get();
}
}
