<?php
namespace App\Services;

use DB;
class ServerSyncService {
    
    public function process($table,$conditions,$data,$autoUpdate) {
       
        $conditionsArray = collect($conditions)
        ->pluck('value','column')
        ->toArray();
        $insertPayLoad = collect($data)
        ->pluck('value','column')
        ->toArray();
       

        if($autoUpdate === 'true') {
            return DB::connection('ekapi')
            ->table($table)
            ->updateOrInsert($conditionsArray, $data);
        }

        else {
            return DB::connection('ekapi')
            ->table($table)
            ->insert($insertPayLoad);
        }


    }


}